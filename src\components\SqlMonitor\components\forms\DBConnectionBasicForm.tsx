import React, { useEffect, useId } from 'react';
import { Form, Input, Select, Card, Row, Col, Button, Switch, InputNumber } from 'antd';
import type { FormInstance } from 'antd';

import type { DBConnection } from '../../types';
import { formStyles } from '../../styles';
import { FORM_BUTTON_TEXT } from '../../constants';

const { Option } = Select;

interface DBConnectionBasicFormProps {
  form: FormInstance;
  initialData?: DBConnection;
  onSubmit?: (values: DBConnection) => void;
  onCancel?: () => void;
  onReset?: () => void;
  loading?: boolean;
}

/**
 * 数据库连接基础信息表单组件
 * 包含数据库连接的基本配置信息
 */
const DBConnectionBasicForm: React.FC<DBConnectionBasicFormProps> = ({ form, initialData, onSubmit, onCancel, onReset, loading = false }) => {
  // 生成唯一ID前缀，避免多个表单实例时的id冲突
  const uniqueId = useId();

  // 监听数据库类型变化
  const dbType = Form.useWatch('db_type', form);

  // 根据数据库类型获取默认端口
  const getDefaultPort = (type: string) => {
    switch (type) {
      case 'mysql':
        return 3306;
      case 'oracle':
        return 1521;
      default:
        return undefined;
    }
  };

  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue({
        name: initialData.name,
        db_type: initialData.db_type,
        host: initialData.host,
        port: initialData.port,
        user: initialData.user,
        passwd: initialData.passwd,
        database: initialData.database,
        use_ssl: initialData.use_ssl,
        server_timezone: initialData.server_timezone,
        instance: initialData.instance,
        connect_method: initialData.connect_method,
      });
    } else {
      form.resetFields();
    }
  }, [initialData, form]);

  // 监听数据库类型变化，自动设置默认端口
  useEffect(() => {
    if (dbType && !initialData) {
      const defaultPort = getDefaultPort(dbType);
      if (defaultPort) {
        form.setFieldValue('port', defaultPort);
      }
    }
  }, [dbType, form, initialData]);

  // 表单提交处理
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      onSubmit?.(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  return (
    <div className="h-full flex flex-col">
      <Form
        form={form}
        layout="vertical"
        className="flex-1 overflow-auto"
        initialValues={{
          server_timezone: 'Asia/Shanghai',
        }}
      >
        <div className={formStyles.tabContent}>
          <Card title="数据库连接信息" size="small" className="mb-4">
            <Row gutter={16} className="mb-4">
              <Col span={12}>
                <Form.Item label="连接名称" name="name" rules={[{ required: true, message: '请输入连接名称' }]}>
                  <Input placeholder="请输入连接名称" id={`${uniqueId}-name`} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="数据库类型" name="db_type" rules={[{ required: true, message: '请选择数据库类型' }]}>
                  <Select placeholder="请选择数据库类型" id={`${uniqueId}-db_type`}>
                    <Option value="mysql">MySQL</Option>
                    <Option value="oracle">Oracle</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={12}>
                <Form.Item label="主机地址" name="host" rules={[{ required: true, message: '请输入主机地址' }]}>
                  <Input placeholder="请输入主机地址" id={`${uniqueId}-host`} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="端口" name="port" rules={[{ required: true, message: '请输入端口' }]}>
                  <InputNumber placeholder="端口" min={1} max={65535} className="w-full!" id={`${uniqueId}-port`} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={12}>
                <Form.Item label="用户名" name="user" rules={[{ required: true, message: '请输入用户名' }]}>
                  <Input placeholder="请输入用户名" id={`${uniqueId}-user`} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="密码" name="passwd" rules={[{ required: true, message: '请输入密码' }]}>
                  <Input.Password placeholder="请输入密码" id={`${uniqueId}-passwd`} />
                </Form.Item>
              </Col>
            </Row>

            {/* MySQL 专用字段 */}
            {dbType === 'mysql' && (
              <>
                <Row gutter={16} className="mb-4">
                  <Col span={12}>
                    <Form.Item label="数据库名" name="database" rules={[{ required: true, message: '请输入数据库名' }]}>
                      <Input placeholder="请输入数据库名" id={`${uniqueId}-database`} />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="服务器时区" name="server_timezone">
                      <Input placeholder="如：Asia/Shanghai" id={`${uniqueId}-server_timezone`} />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={16} className="mb-4">
                  <Col span={12}>
                    <Form.Item label="启用SSL" name="use_ssl" valuePropName="checked">
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            {/* Oracle 专用字段 */}
            {dbType === 'oracle' && (
              <>
                <Row gutter={16} className="mb-4">
                  <Col span={12}>
                    <Form.Item label="实例名" name="instance" rules={[{ required: true, message: '请输入实例名' }]}>
                      <Input placeholder="请输入实例名" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="连接方式" name="connect_method" rules={[{ required: true, message: '请选择连接方式' }]}>
                      <Select placeholder="请选择连接方式" id={`${uniqueId}-connect_method`}>
                        <Option value="sid">SID</Option>
                        <Option value="service">Service Name</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}
          </Card>
        </div>
      </Form>

      {/* 底部操作栏 */}
      <div className={formStyles.footerContainer}>
        <div className="flex justify-between items-center">
          <div className={formStyles.footerHint}>{initialData ? '编辑数据库连接' : '创建新连接'}</div>
          <div className={formStyles.buttonGroup}>
            <Button onClick={onCancel} className={`${formStyles.actionButton} ${formStyles.cancelButton}`}>
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            {!initialData && (
              <Button onClick={handleReset} className={`${formStyles.actionButton} ${formStyles.resetButton}`}>
                {FORM_BUTTON_TEXT.reset}
              </Button>
            )}
            <Button type="primary" loading={loading} onClick={handleSubmit} className={`${formStyles.actionButton} ${initialData ? formStyles.confirmButton : formStyles.submitButton}`}>
              {initialData ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DBConnectionBasicForm;
