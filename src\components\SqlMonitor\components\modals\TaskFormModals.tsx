import React, { useEffect, useId } from 'react';
import { Modal, Form, Input, Select, Row, Col, Button } from 'antd';

// 导入重构后的模块
import type { DBConnection, AlertSend } from '../../types';
import { DB_TYPE_OPTIONS, SEND_TYPE_OPTIONS } from '../../constants';

const { Option } = Select;

interface DbConnectionModalProps {
  visible: boolean;
  editingData?: DBConnection;
  onCancel: () => void;
  onSubmit: (data: DBConnection) => void;
}

interface AlertSendModalProps {
  visible: boolean;
  editingData?: AlertSend;
  onCancel: () => void;
  onSubmit: (data: AlertSend) => void;
}

// 数据库连接Modal
export const DbConnectionModal: React.FC<DbConnectionModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();
  const uniqueId = useId();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const dbData: DBConnection = {
      id: editingData?.id || Date.now(),
      ...values,
      create_time: editingData?.create_time || new Date().toISOString(),
      update_time: new Date().toISOString(),
    };
    onSubmit(dbData);
  };

  return (
    <Modal title={editingData ? '编辑数据库连接' : '新增数据库连接'} open={visible} onCancel={onCancel} footer={null} width={800}>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="连接名称" name="name" rules={[{ required: true, message: '请输入连接名称' }]}>
              <Input placeholder="请输入连接名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="数据库类型" name="db_type" rules={[{ required: true, message: '请选择数据库类型' }]}>
              <Select placeholder="请选择数据库类型" id={`${uniqueId}-db_type`}>
                {DB_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="主机地址" name="host" rules={[{ required: true, message: '请输入主机地址' }]}>
              <Input placeholder="请输入主机地址" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="端口" name="port" rules={[{ required: true, message: '请输入端口' }]}>
              <Input placeholder="请输入端口" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="用户名" name="user" rules={[{ required: true, message: '请输入用户名' }]}>
              <Input placeholder="请输入用户名" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="密码" name="passwd" rules={[{ required: true, message: '请输入密码' }]}>
              <Input.Password placeholder="请输入密码" />
            </Form.Item>
          </Col>
        </Row>
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" htmlType="submit">
            {editingData ? '更新' : '创建'}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 告警发送Modal
export const AlertSendModal: React.FC<AlertSendModalProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();
  const uniqueId = useId();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const sendData: AlertSend = {
      id: editingData?.id || Date.now(),
      ...values,
      create_time: editingData?.create_time || new Date().toISOString(),
      update_time: new Date().toISOString(),
    };
    onSubmit(sendData);
  };

  return (
    <Modal title={editingData ? '编辑告警发送' : '新增告警发送'} open={visible} onCancel={onCancel} footer={null} width={600}>
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="发送名称" name="name" rules={[{ required: true, message: '请输入发送名称' }]}>
              <Input placeholder="请输入发送名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="发送类型" name="type" rules={[{ required: true, message: '请选择发送类型' }]}>
              <Select placeholder="请选择发送类型" id={`${uniqueId}-send_type`}>
                {SEND_TYPE_OPTIONS.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>
        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" htmlType="submit">
            {editingData ? '更新' : '创建'}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

// 默认导出数据库连接Modal组件
export default DbConnectionModal;
